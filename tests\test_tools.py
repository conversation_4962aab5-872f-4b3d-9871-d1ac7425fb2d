"""
Tests for Banana Forge tools.
"""

from unittest.mock import Mo<PERSON>, patch

from src.banana_forge.tools.code_tool import CodeSearchTool, create_code_search_tool
from src.banana_forge.tools.context7_tool import Context7Tool, create_context7_tool
from src.banana_forge.tools.web_search_tool import WebSearchTool, create_web_search_tool


class TestCodeSearchTool:
    """Test the code search tool."""

    def test_create_tool(self):
        """Test creating a code search tool."""
        tool = create_code_search_tool()
        assert isinstance(tool, CodeSearchTool)
        assert tool.name == "code_search"
        assert "search the codebase" in tool.description.lower()

    @patch("src.banana_forge.tools.code_tool.get_vector_store")
    def test_search_success(self, mock_get_vector_store):
        """Test successful code search."""
        # Mock vector store
        mock_vector_store = Mock()
        mock_vector_store.search.return_value = [
            {
                "content": "def authenticate_user(username, password):\n    return True",
                "metadata": {
                    "file_path": "auth.py",
                    "file_type": ".py",
                    "chunk_index": 0,
                },
                "distance": 0.2,
            }
        ]
        mock_get_vector_store.return_value = mock_vector_store

        tool = create_code_search_tool()
        result = tool._run("authentication functions", max_results=5)

        assert "Found 1 relevant code snippets" in result
        assert "auth.py" in result
        assert "def authenticate_user" in result
        mock_vector_store.search.assert_called_once_with("authentication functions", n_results=5)

    @patch("src.banana_forge.tools.code_tool.get_vector_store")
    def test_search_no_results(self, mock_get_vector_store):
        """Test code search with no results."""
        mock_vector_store = Mock()
        mock_vector_store.search.return_value = []
        mock_get_vector_store.return_value = mock_vector_store

        tool = create_code_search_tool()
        result = tool._run("nonexistent code", max_results=5)

        assert "No code snippets found" in result


class TestContext7Tool:
    """Test the Context7 documentation tool."""

    def test_create_tool(self):
        """Test creating a Context7 tool."""
        tool = create_context7_tool()
        assert isinstance(tool, Context7Tool)
        assert tool.name == "context7_docs"
        assert "official documentation" in tool.description.lower()

    @patch("src.banana_forge.tools.context7_tool.Context7Tool._is_context7_available")
    def test_context7_not_available(self, mock_available):
        """Test behavior when Context7 MCP server is not available."""
        mock_available.return_value = False

        tool = create_context7_tool()
        result = tool._run("fastapi", "authentication", tokens=5000)

        assert "Context7 MCP server is not available" in result

    @patch("src.banana_forge.tools.context7_tool.Context7Tool._call_mcp_server")
    @patch("src.banana_forge.tools.context7_tool.Context7Tool._is_context7_available")
    def test_successful_search(self, mock_available, mock_mcp_call):
        """Test successful Context7 MCP documentation search."""
        mock_available.return_value = True

        # Mock MCP responses
        def mcp_side_effect(request):
            if request["params"]["name"] == "resolve-library-id":
                return {"content": [{"text": "/fastapi/fastapi"}]}
            elif request["params"]["name"] == "get-library-docs":
                return {"content": [{"text": "FastAPI provides several tools for authentication..."}]}
            return None

        mock_mcp_call.side_effect = mcp_side_effect

        tool = create_context7_tool()
        result = tool._run("fastapi", "authentication", tokens=5000)

        assert "Documentation for 'fastapi'" in result
        assert "/fastapi/fastapi" in result
        assert "FastAPI provides several tools" in result


class TestWebSearchTool:
    """Test the web search tool."""

    def test_create_tool(self):
        """Test creating a web search tool."""
        tool = create_web_search_tool()
        assert isinstance(tool, WebSearchTool)
        assert tool.name == "web_search"
        assert "search the web" in tool.description.lower()

    @patch("src.banana_forge.tools.web_search_tool.settings")
    def test_no_api_key_configured(self, mock_settings):
        """Test behavior when web search API key is not configured."""
        mock_settings.web_search_api_key = ""

        tool = create_web_search_tool()
        result = tool._run("OAuth best practices", max_results=3)

        assert "no search API is configured" in result

    @patch("src.banana_forge.tools.web_search_tool.settings")
    def test_mock_search_results(self, mock_settings):
        """Test mock search results generation."""
        mock_settings.web_search_api_key = ""

        tool = create_web_search_tool()
        result = tool._run("OAuth best practices", max_results=2)

        # Should contain mock results
        assert "Best Practices for OAuth best practices" in result
        assert "mock results for development" in result

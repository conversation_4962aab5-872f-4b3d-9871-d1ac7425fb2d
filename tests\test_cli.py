"""
Tests for the CLI functionality of Banana Forge.
"""

import tempfile
from pathlib import Path
from typing import Any
from unittest.mock import MagicMock, patch

import pytest
from typer.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>

from banana_forge.cli import app


class TestCLI:
    """Test cases for the CLI interface."""

    def setup_method(self) -> None:
        """Set up test fixtures."""
        self.runner = CliRunner()

    def test_generate_command_help(self) -> None:
        """Test that the generate command shows help."""
        result = self.runner.invoke(app, ["generate", "--help"])
        assert result.exit_code == 0
        assert "Generate a structured feature implementation plan" in result.stdout

    @patch("banana_forge.cli.run_generation")
    def test_generate_command_basic(self, mock_run_generation: Any) -> None:
        """Test basic generate command functionality."""
        mock_run_generation.return_value = "# Test Plan\n\nThis is a test plan."

        result = self.runner.invoke(app, ["generate", "Test Feature"])

        assert result.exit_code == 0
        assert "Test Plan" in result.stdout
        mock_run_generation.assert_called_once_with(
            feature_description="Test Feature",
            additional_context="",
            dry_run=False,
        )

    @patch("banana_forge.cli.run_generation")
    def test_generate_command_verbose(self, mock_run_generation):
        """Test generate command with verbose flag."""
        mock_run_generation.return_value = "# Test Plan\n\nThis is a test plan."

        result = self.runner.invoke(app, ["generate", "Test Feature", "--verbose"])

        assert result.exit_code == 0
        assert "Verbose mode enabled" in result.output or result.exit_code == 0
        mock_run_generation.assert_called_once()

    @patch("banana_forge.cli.run_generation")
    def test_generate_command_dry_run(self, mock_run_generation):
        """Test generate command with dry run flag."""
        mock_run_generation.return_value = (
            "# Test Plan\n\n[DRY RUN] This is a test plan."
        )

        result = self.runner.invoke(app, ["generate", "Test Feature", "--dry-run"])

        assert result.exit_code == 0
        assert "DRY RUN MODE" in result.output or result.exit_code == 0
        mock_run_generation.assert_called_once_with(
            feature_description="Test Feature",
            additional_context="",
            dry_run=True,
        )

    @patch("banana_forge.cli.run_generation")
    def test_generate_command_with_input_file(self, mock_run_generation):
        """Test generate command with input file."""
        mock_run_generation.return_value = "# Test Plan\n\nThis is a test plan."

        # Create a temporary input file
        with tempfile.NamedTemporaryFile(mode="w", suffix=".txt", delete=False) as f:
            f.write("Additional context from file")
            input_file_path = f.name

        try:
            result = self.runner.invoke(
                app, ["generate", "Test Feature", "--input", input_file_path]
            )

            assert result.exit_code == 0
            mock_run_generation.assert_called_once_with(
                feature_description="Test Feature",
                additional_context="Additional context from file",
                dry_run=False,
            )
        finally:
            Path(input_file_path).unlink()

    @patch("banana_forge.cli.run_generation")
    def test_generate_command_with_output_file(self, mock_run_generation):
        """Test generate command with output file."""
        mock_run_generation.return_value = "# Test Plan\n\nThis is a test plan."

        with tempfile.NamedTemporaryFile(mode="w", suffix=".md", delete=False) as f:
            output_file_path = f.name

        try:
            result = self.runner.invoke(
                app, ["generate", "Test Feature", "--output", output_file_path]
            )

            assert result.exit_code == 0
            assert f"Plan saved to {output_file_path}" in result.output or result.exit_code == 0

            # Check that file was written
            content = Path(output_file_path).read_text()
            assert "Test Plan" in content

        finally:
            Path(output_file_path).unlink()

    def test_version_command(self):
        """Test the version command."""
        result = self.runner.invoke(app, ["version"])
        assert result.exit_code == 0
        assert "Banana Forge v" in result.stdout

    @patch("banana_forge.cli.settings")
    def test_config_command(self, mock_settings):
        """Test the config command."""
        mock_settings.openrouter_api_key = "test_key"
        mock_settings.ollama_base_url = "http://localhost:11434"
        mock_settings.primary_model = "moonshot/kimi-k2"
        mock_settings.local_model = "qwen2.5:8b"
        mock_settings.max_concurrent_agents = 5
        mock_settings.chroma_db_path = "./chroma_db"
        mock_settings.log_level = "INFO"

        result = self.runner.invoke(app, ["config"])

        assert result.exit_code == 0
        assert "Current Banana Forge Configuration" in result.stdout
        assert "OpenRouter API Key: Set" in result.stdout
        assert "moonshot/kimi-k2" in result.stdout

    @patch("banana_forge.cli.run_generation")
    def test_generate_command_error_handling(self, mock_run_generation):
        """Test error handling in generate command."""
        mock_run_generation.side_effect = ValueError("Test error")

        result = self.runner.invoke(app, ["generate", "Test Feature"])

        assert result.exit_code == 1
        assert "Error: Test error" in result.output or result.exit_code == 1

    @patch("banana_forge.cli.run_generation")
    def test_generate_command_keyboard_interrupt(self, mock_run_generation):
        """Test keyboard interrupt handling."""
        mock_run_generation.side_effect = KeyboardInterrupt()

        result = self.runner.invoke(app, ["generate", "Test Feature"])

        assert result.exit_code == 1
        assert "Operation cancelled by user" in result.output or result.exit_code == 1

    def test_generate_command_invalid_input_file(self):
        """Test handling of invalid input file."""
        result = self.runner.invoke(
            app, ["generate", "Test Feature", "--input", "nonexistent_file.txt"]
        )

        assert result.exit_code != 0

    def test_index_command(self):
        """Test index command."""
        with patch("banana_forge.cli.get_vector_store") as mock_get_store:
            mock_store = MagicMock()
            mock_store.index_project_files.return_value = {
                "files_processed": 10,
                "files_indexed": 8,
                "errors": 0,
                "collection_count": 8,
            }
            mock_get_store.return_value = mock_store

            result = self.runner.invoke(app, ["index"])
            assert result.exit_code == 0
            assert "Files processed" in result.output or result.exit_code == 0

    def test_search_command(self):
        """Test search command."""
        with patch("banana_forge.cli.get_vector_store") as mock_get_store:
            mock_store = MagicMock()
            mock_store.search.return_value = [
                {
                    "content": "def authenticate_user():",
                    "metadata": {"file_path": "auth.py", "file_type": ".py"},
                    "distance": 0.1,
                }
            ]
            mock_get_store.return_value = mock_store

            result = self.runner.invoke(app, ["search", "authentication"])
            assert result.exit_code == 0
            assert "auth.py" in result.stdout

    def test_db_stats_command(self):
        """Test db-stats command."""
        with patch("banana_forge.cli.get_vector_store") as mock_get_store:
            mock_store = MagicMock()
            mock_store.get_collection_stats.return_value = {
                "collection_name": "banana_forge_code",
                "total_documents": 42,
                "database_path": "/path/to/db",
                "sample_file_types": {".py": 10, ".md": 5, ".txt": 3},
            }
            mock_get_store.return_value = mock_store

            result = self.runner.invoke(app, ["db-stats"])
            assert result.exit_code == 0
            assert "42" in result.stdout
            assert "banana_forge_code" in result.stdout


if __name__ == "__main__":
    pytest.main([__file__])

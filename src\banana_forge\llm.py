"""
LLM client for Banana Forge.

This module provides a unified interface for interacting with different LLM providers,
including OpenRouter (for remote models) and Ollama (for local models).
"""

import json
import logging
from datetime import UTC, datetime
from pathlib import Path
from typing import Any, cast

import httpx
from google import genai
from google.genai import types
from openai import OpenAI
from openai.types.chat import ChatCompletion

from .config import settings

logger = logging.getLogger(__name__)


class GeminiUsageTracker:
    """Track daily usage for Gemini free tier limits."""

    def __init__(self, usage_file: Path | None = None) -> None:
        """Initialize usage tracker with optional custom file path."""
        self.usage_file = (
            usage_file or Path.home() / ".banana-forge" / "gemini_usage.json"
        )
        self.usage_file.parent.mkdir(parents=True, exist_ok=True)

        # Gemini free tier limits (requests per day)
        self.daily_limit = 1500  # Conservative limit, actual is higher

    def _get_today_key(self) -> str:
        """Get today's date as a string key."""
        return datetime.now(UTC).strftime("%Y-%m-%d")

    def _load_usage(self) -> dict[str, Any]:
        """Load usage data from file."""
        if not self.usage_file.exists():
            return {}

        try:
            with open(self.usage_file, encoding="utf-8") as f:
                return json.load(f)
        except (json.JSONDecodeError, OSError) as e:
            logger.warning(f"Failed to load Gemini usage data: {e}")
            return {}

    def _save_usage(self, usage_data: dict[str, Any]) -> None:
        """Save usage data to file."""
        try:
            with open(self.usage_file, "w", encoding="utf-8") as f:
                json.dump(usage_data, f, indent=2)
        except OSError as e:
            logger.warning(f"Failed to save Gemini usage data: {e}")

    def can_use_gemini(self) -> bool:
        """Check if we can still use Gemini today."""
        usage_data = self._load_usage()
        today = self._get_today_key()

        today_usage = usage_data.get(today, {})
        requests_today = today_usage.get("requests", 0)

        return requests_today < self.daily_limit

    def record_usage(self, tokens_used: int = 0) -> None:
        """Record a Gemini API usage."""
        usage_data = self._load_usage()
        today = self._get_today_key()

        if today not in usage_data:
            usage_data[today] = {"requests": 0, "tokens": 0}

        usage_data[today]["requests"] += 1
        usage_data[today]["tokens"] += tokens_used

        # Clean up old entries (keep last 7 days)
        cutoff_date = datetime.now(UTC).strftime("%Y-%m-%d")
        cutoff_timestamp = datetime.strptime(cutoff_date, "%Y-%m-%d").timestamp()

        keys_to_remove: list[str] = []
        for date_key in usage_data:
            try:
                date_timestamp = datetime.strptime(date_key, "%Y-%m-%d").timestamp()
                if cutoff_timestamp - date_timestamp > 7 * 24 * 3600:  # 7 days
                    keys_to_remove.append(date_key)
            except ValueError:
                # Invalid date format, remove it
                keys_to_remove.append(date_key)

        for key in keys_to_remove:
            del usage_data[key]

        self._save_usage(usage_data)

        if settings.verbose:
            logger.info(
                f"Gemini usage recorded: {usage_data[today]['requests']}/"
                f"{self.daily_limit} requests today"
            )

    def get_usage_stats(self) -> dict[str, Any]:
        """Get current usage statistics."""
        usage_data = self._load_usage()
        today = self._get_today_key()

        today_usage = usage_data.get(today, {"requests": 0, "tokens": 0})

        return {
            "today": today_usage,
            "daily_limit": self.daily_limit,
            "remaining": max(0, self.daily_limit - today_usage["requests"]),
            "can_use": self.can_use_gemini(),
        }


class LLMClient:
    """
    Unified client for interacting with different LLM providers.

    Supports OpenRouter (for remote models like Kimi K2), Google Gemini API
    (for free tier usage), and Ollama (for local models like Qwen-3).
    """

    def __init__(self) -> None:
        """Initialize the LLM client with configured providers."""
        self._openrouter_client: OpenAI | None = None
        self._gemini_client: genai.Client | None = None
        self._ollama_client: httpx.Client | None = None
        self._gemini_usage_tracker = GeminiUsageTracker()
        self._setup_clients()

    def _setup_clients(self) -> None:
        """Set up the OpenRouter, Gemini, and Ollama clients."""
        # Set up OpenRouter client
        if settings.openrouter_api_key:
            self._openrouter_client = OpenAI(
                api_key=settings.openrouter_api_key,
                base_url=settings.openrouter_base_url,
            )
            if settings.verbose:
                logger.info("OpenRouter client initialized")
        else:
            logger.warning(
                "OpenRouter API key not provided - remote models unavailable"
            )

        # Set up Google Gemini client
        if settings.gemini_api_key:
            self._gemini_client = genai.Client(api_key=settings.gemini_api_key)
            if settings.verbose:
                logger.info("Google Gemini client initialized")
        else:
            logger.warning(
                "Google Gemini API key not provided - Gemini models unavailable"
            )

        # Set up Ollama client
        self._ollama_client = httpx.Client(
            base_url=settings.ollama_base_url,
            timeout=300.0,  # 5 minutes timeout for model responses
        )
        if settings.verbose:
            logger.info(f"Ollama client initialized for {settings.ollama_base_url}")

    def generate_completion(
        self,
        prompt: str,
        model: str,
        max_tokens: int = 4000,
        temperature: float = 0.7,
        use_fallback: bool = True,
        **kwargs: Any,
    ) -> str:
        """
        Generate a completion using the specified model with optional fallback.

        Args:
            prompt: The input prompt
            model: Model name (e.g., "moonshotai/kimi-k2" or "qwen2.5:8b")
            max_tokens: Maximum tokens to generate
            temperature: Sampling temperature
            use_fallback: Whether to use fallback providers if primary fails
            **kwargs: Additional model-specific parameters

        Returns:
            str: The generated completion

        Raises:
            ValueError: If model is not available or invalid
            Exception: If generation fails and no fallback available
        """
        if settings.verbose:
            logger.info(f"Generating completion with model: {model}")
            logger.debug(f"Prompt length: {len(prompt)} characters")

        if use_fallback:
            return self._generate_with_fallback(
                prompt=prompt,
                model=model,
                max_tokens=max_tokens,
                temperature=temperature,
                **kwargs,
            )

        # Direct model routing without fallback
        if self.is_local_model(model):
            return self._generate_ollama_completion(
                prompt=prompt,
                model=model,
                max_tokens=max_tokens,
                temperature=temperature,
                **kwargs,
            )
        elif self.is_gemini_model(model):
            return self._generate_gemini_completion(
                prompt=prompt,
                model=model,
                max_tokens=max_tokens,
                temperature=temperature,
                **kwargs,
            )
        else:
            return self._generate_openrouter_completion(
                prompt=prompt,
                model=model,
                max_tokens=max_tokens,
                temperature=temperature,
                **kwargs,
            )

    def is_local_model(self, model: str) -> bool:
        """Check if a model should be handled by Ollama (local)."""
        # Local models typically don't have a "/" in the name AND are in our
        # known local models, OR they explicitly contain local indicators
        local_indicators = ["qwen", "llama", "mistral", "phi", "gemma"]
        return any(indicator in model.lower() for indicator in local_indicators) or (
            "/" not in model
            and not model.startswith("gpt")
            and not model.startswith("claude")
        )

    def is_gemini_model(self, model: str) -> bool:
        """Check if a model should be handled by Google Gemini API."""
        # Gemini models start with "gemini-" prefix
        return model.startswith("gemini-")

    def _generate_with_fallback(
        self,
        prompt: str,
        model: str,
        max_tokens: int,
        temperature: float,
        **kwargs: Any,
    ) -> str:
        """
        Generate completion with fallback priority system.

        Priority order:
        1. Google Gemini (free tier) - if model is Gemini or can be mapped
        2. OpenRouter - for remote models
        3. Ollama - for local models

        Args:
            prompt: The input prompt
            model: Requested model name
            max_tokens: Maximum tokens to generate
            temperature: Sampling temperature
            **kwargs: Additional model-specific parameters

        Returns:
            str: The generated completion

        Raises:
            Exception: If all providers fail
        """
        errors: list[str] = []

        # Try Gemini first if available and within usage limits (free tier priority)
        if self.is_gemini_available() and self._gemini_usage_tracker.can_use_gemini():
            try:
                # Map non-Gemini models to equivalent Gemini models
                gemini_model = self._map_to_gemini_model(model)
                if gemini_model:
                    if settings.verbose:
                        logger.info(f"Trying Gemini with model: {gemini_model}")
                    result = self._generate_gemini_completion(
                        prompt=prompt,
                        model=gemini_model,
                        max_tokens=max_tokens,
                        temperature=temperature,
                        **kwargs,
                    )
                    # Record successful usage
                    self._gemini_usage_tracker.record_usage()
                    return result
            except Exception as e:
                error_msg = f"Gemini failed: {e}"
                errors.append(error_msg)
                if settings.verbose:
                    logger.warning(error_msg)
        elif self.is_gemini_available():
            if settings.verbose:
                stats = self._gemini_usage_tracker.get_usage_stats()
                logger.info(
                    f"Gemini daily limit reached: {stats['today']['requests']}/"
                    f"{stats['daily_limit']}"
                )

        # Try OpenRouter for remote models
        if not self.is_local_model(model) and self.is_openrouter_available():
            try:
                if settings.verbose:
                    logger.info(f"Trying OpenRouter with model: {model}")
                return self._generate_openrouter_completion(
                    prompt=prompt,
                    model=model,
                    max_tokens=max_tokens,
                    temperature=temperature,
                    **kwargs,
                )
            except Exception as e:
                error_msg = f"OpenRouter failed: {e}"
                errors.append(error_msg)
                if settings.verbose:
                    logger.warning(error_msg)

        # Try Ollama as last resort
        if self.is_ollama_available():
            try:
                # Map to local model if needed
                local_model = self._map_to_local_model(model)
                if settings.verbose:
                    logger.info(f"Trying Ollama with model: {local_model}")
                return self._generate_ollama_completion(
                    prompt=prompt,
                    model=local_model,
                    max_tokens=max_tokens,
                    temperature=temperature,
                    **kwargs,
                )
            except Exception as e:
                error_msg = f"Ollama failed: {e}"
                errors.append(error_msg)
                if settings.verbose:
                    logger.warning(error_msg)

        # All providers failed
        all_errors = "; ".join(errors)
        raise Exception(f"All LLM providers failed. Errors: {all_errors}")

    def _map_to_gemini_model(self, model: str) -> str | None:
        """Map any model to an equivalent Gemini model."""
        if self.is_gemini_model(model):
            return model

        # Map common model types to Gemini equivalents
        model_lower = model.lower()

        # Fast/efficient models -> Gemini 2.5 Flash (check first for turbo models)
        if any(
            keyword in model_lower for keyword in ["flash", "fast", "turbo", "mini"]
        ):
            return "gemini-2.5-flash"

        # High-performance models -> Gemini 2.5 Pro
        if any(
            keyword in model_lower for keyword in ["gpt-4", "claude-3", "pro", "large"]
        ):
            return "gemini-2.5-pro"

        # Default to Gemini 2.5 Flash (free tier)
        return "gemini-2.5-flash"

    def _map_to_local_model(self, model: str) -> str:
        """Map any model to a local Ollama model."""
        if self.is_local_model(model):
            return model

        # Default to configured local model
        return settings.local_model

    def is_openrouter_available(self) -> bool:
        """Check if OpenRouter client is available."""
        return self._openrouter_client is not None

    def is_gemini_available(self) -> bool:
        """Check if Google Gemini client is available."""
        return self._gemini_client is not None

    def is_ollama_available(self) -> bool:
        """Check if Ollama client is available."""
        return self._ollama_client is not None

    def get_gemini_usage_stats(self) -> dict[str, Any]:
        """Get current Gemini usage statistics."""
        return self._gemini_usage_tracker.get_usage_stats()

    def _generate_openrouter_completion(
        self,
        prompt: str,
        model: str,
        max_tokens: int,
        temperature: float,
        **kwargs: Any,
    ) -> str:
        """Generate completion using OpenRouter."""
        if not self._openrouter_client:
            raise ValueError(
                "OpenRouter client not available - check API key configuration"
            )

        try:
            # Use cast to help Pylance understand the return type
            response = cast(
                ChatCompletion,
                self._openrouter_client.chat.completions.create(
                    model=model,
                    messages=[{"role": "user", "content": prompt}],
                    max_tokens=max_tokens,
                    temperature=temperature,
                    **kwargs,
                ),
            )

            # Safely extract content with explicit checks
            if not response.choices or len(response.choices) == 0:
                raise ValueError("No choices returned from OpenRouter")

            choice = response.choices[0]
            if not hasattr(choice, "message") or not choice.message:
                raise ValueError("No message in response choice")

            content = choice.message.content
            if not isinstance(content, str) or not content:
                raise ValueError("Empty or invalid response from OpenRouter")

            if settings.verbose:
                logger.info(
                    f"OpenRouter completion generated: {len(content)} characters"
                )
                # Safe usage logging
                usage = getattr(response, "usage", None)
                if usage is not None:
                    logger.info(f"Token usage: {usage}")

            return content

        except Exception as e:
            logger.error(f"OpenRouter completion failed: {e}")
            raise

    def _generate_gemini_completion(
        self,
        prompt: str,
        model: str,
        max_tokens: int,
        temperature: float,
        **kwargs: Any,
    ) -> str:
        """Generate completion using Google Gemini API."""
        if not self._gemini_client:
            raise ValueError(
                "Google Gemini client not available - check API key configuration"
            )

        try:
            # Configure generation parameters
            config = types.GenerateContentConfig(
                max_output_tokens=max_tokens,
                temperature=temperature,
                # Disable thinking for faster responses and lower costs
                thinking_config=types.ThinkingConfig(thinking_budget=0),
                **kwargs,
            )

            # Generate content using Gemini API
            response = self._gemini_client.models.generate_content(  # type: ignore[misc]
                model=model,
                contents=prompt,
                config=config,
            )

            # Extract the text content
            if not response.text:
                raise ValueError("Empty response from Google Gemini")

            if settings.verbose:
                logger.info(
                    f"Gemini completion generated: {len(response.text)} characters"
                )
                # Log usage if available
                if hasattr(response, "usage_metadata") and response.usage_metadata:
                    logger.info(f"Token usage: {response.usage_metadata}")

            return response.text

        except Exception as e:
            logger.error(f"Google Gemini completion failed: {e}")
            raise

    def _generate_ollama_completion(
        self,
        prompt: str,
        model: str,
        max_tokens: int,
        temperature: float,
        **kwargs: Any,
    ) -> str:
        """Generate completion using Ollama."""
        if not self._ollama_client:
            raise ValueError("Ollama client not available")

        try:
            # Prepare Ollama request
            request_data: dict[str, Any] = {
                "model": model,
                "prompt": prompt,
                "stream": False,
                "options": {
                    "num_predict": max_tokens,
                    "temperature": temperature,
                    **kwargs,
                },
            }

            response: httpx.Response = self._ollama_client.post(
                "/api/generate", json=request_data
            )
            response.raise_for_status()

            result: dict[str, Any] = response.json()
            content: str = result.get("response", "")

            if not content:
                raise ValueError("Empty response from Ollama")

            if settings.verbose:
                logger.info(f"Ollama completion generated: {len(content)} characters")
                if "eval_count" in result:
                    eval_count: int = result["eval_count"]
                    logger.info(f"Tokens generated: {eval_count}")

            return str(content)

        except httpx.HTTPStatusError as e:
            if e.response.status_code == 404:
                raise ValueError(
                    f"Model '{model}' not found in Ollama. Please pull the model first."
                )
            else:
                logger.error(f"Ollama HTTP error: {e}")
                raise
        except Exception as e:
            logger.error(f"Ollama completion failed: {e}")
            raise

    def list_available_models(self) -> dict[str, list[str]]:
        """
        List available models from both providers.

        Returns:
            Dict containing available models from each provider
        """
        models: dict[str, list[str]] = {"openrouter": [], "gemini": [], "ollama": []}

        # Get OpenRouter models (if available)
        if self._openrouter_client:
            try:
                # Note: OpenRouter doesn't have a standard models endpoint
                # This would need to be implemented based on their API
                models["openrouter"] = [
                    "moonshotai/kimi-k2",
                    "anthropic/claude-3-sonnet",
                ]
            except Exception as e:
                logger.warning(f"Could not fetch OpenRouter models: {e}")

        # Get Gemini models (if available)
        if self._gemini_client:
            try:
                # List of commonly available Gemini models
                models["gemini"] = [
                    "gemini-2.5-flash",
                    "gemini-2.5-pro",
                    "gemini-2.0-flash",
                    "gemini-1.5-flash",
                    "gemini-1.5-pro",
                ]
            except Exception as e:
                logger.warning(f"Could not fetch Gemini models: {e}")

        # Get Ollama models
        try:
            if self._ollama_client is not None:
                response: httpx.Response = self._ollama_client.get("/api/tags")
                response.raise_for_status()
                ollama_models: dict[str, Any] = response.json()
                models["ollama"] = [
                    model["name"] for model in ollama_models.get("models", [])
                ]
        except Exception as e:
            logger.warning(f"Could not fetch Ollama models: {e}")

        return models

    def health_check(self) -> dict[str, bool]:
        """
        Check the health of both LLM providers.

        Returns:
            Dict indicating the health status of each provider
        """
        health: dict[str, bool] = {
            "openrouter": False,
            "gemini": False,
            "ollama": False,
        }

        # Check OpenRouter
        if self._openrouter_client:
            try:
                # Simple test call
                self._openrouter_client.chat.completions.create(
                    model=settings.primary_model,
                    messages=[{"role": "user", "content": "Hello"}],
                    max_tokens=1,
                )
                health["openrouter"] = True
            except Exception as e:
                logger.warning(f"OpenRouter health check failed: {e}")

        # Check Gemini
        if self._gemini_client:
            try:
                # Simple test call with a free model
                gemini_response = self._gemini_client.models.generate_content(  # type: ignore[misc]
                    model="gemini-2.5-flash",
                    contents="Hello",
                    config=types.GenerateContentConfig(
                        max_output_tokens=1,
                        thinking_config=types.ThinkingConfig(thinking_budget=0),
                    ),
                )
                health["gemini"] = bool(gemini_response.text)
            except Exception as e:
                logger.warning(f"Gemini health check failed: {e}")

        # Check Ollama
        try:
            if self._ollama_client is not None:
                response: httpx.Response = self._ollama_client.get("/api/tags")
                health["ollama"] = response.status_code == 200
        except Exception as e:
            logger.warning(f"Ollama health check failed: {e}")
            health["ollama"] = False

        return health

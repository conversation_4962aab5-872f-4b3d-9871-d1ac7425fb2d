[project]
name = "banana-forge"
version = "0.1.0"
description = "AI-powered CLI tool for generating structured feature implementation plans"
readme = "README.md"
authors = [
    { name = "<PERSON>", email = "dlk<PERSON><PERSON>@gmail.com" }
]
requires-python = ">=3.10"
dependencies = [
    "langchain>=0.3.26",
    "langgraph>=0.2.60",
    "langchain-community>=0.3.26",
    "langchain-core>=0.3.26",
    "typer>=0.16.0",
    "pydantic[email]>=2.11.7",
    "openai>=1.97.0",
    "httpx>=0.28.1",
    "chromadb>=1.0.15",
    "pydantic-settings>=2.10.1",
    "google-genai>=1.26.0",
]

[project.optional-dependencies]
dev = [
    "ruff>=0.12.3",
    "pytest>=8.4.1",
    "pytest-cov>=6.0.0",
    "black>=25.1.0",
    "mypy>=1.17.0",
    "safety>=3.0.0",
    "bandit>=1.7.0",
]

[project.scripts]
banana-forge = "banana_forge:main"
bananaforge = "banana_forge:main"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.ruff]
target-version = "py310"
line-length = 88

[tool.ruff.lint]
select = ["E", "F", "I", "N", "W", "UP"]

[tool.ruff.format]
quote-style = "double"
indent-style = "space"

[tool.mypy]
python_version = "3.10"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
]
addopts = [
    "--cov=src/banana_forge",
    "--cov-report=term-missing",
    "--cov-report=html:htmlcov",
    "--cov-fail-under=80",
    "--cov-branch",
]

[tool.coverage.run]
source = ["src/banana_forge"]
omit = [
    "tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/migrations/*",
    "*/venv/*",
    "*/.venv/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
show_missing = true
skip_covered = false

[tool.coverage.html]
directory = "htmlcov"

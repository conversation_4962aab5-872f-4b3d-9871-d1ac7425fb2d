"""Tests for agent definitions."""

import pytest
from unittest.mock import Mock, patch

from banana_forge.agents.agent_definitions import create_specialized_agents


class TestAgentDefinitions:
    """Test cases for agent definitions."""

    @patch('banana_forge.agents.agent_definitions.LLMClient')
    def test_create_specialized_agents(self, mock_llm_class):
        """Test creation of specialized agents."""
        mock_llm = Mock()
        mock_llm_class.return_value = mock_llm
        
        agents = create_specialized_agents()
        
        assert isinstance(agents, dict)
        assert len(agents) > 0
        
        # Check that we have expected agent types
        expected_agents = ["core_agent", "ui_agent", "testing_agent", "documentation_agent"]
        for agent_name in expected_agents:
            assert agent_name in agents
            assert agents[agent_name] is not None

    @patch('banana_forge.agents.agent_definitions.LLMClient')
    def test_agent_creation_with_tools(self, mock_llm_class):
        """Test that agents are created with appropriate tools."""
        mock_llm = Mock()
        mock_llm_class.return_value = mock_llm
        
        with patch('banana_forge.agents.agent_definitions.create_code_search_tool') as mock_code_tool:
            with patch('banana_forge.agents.agent_definitions.create_web_search_tool') as mock_web_tool:
                mock_code_tool.return_value = Mock()
                mock_web_tool.return_value = Mock()
                
                agents = create_specialized_agents()
                
                # Tools should be created
                mock_code_tool.assert_called()
                mock_web_tool.assert_called()
                
                # Agents should be created
                assert len(agents) > 0

    def test_agent_creation_error_handling(self):
        """Test error handling in agent creation."""
        with patch('banana_forge.agents.agent_definitions.LLMClient', side_effect=Exception("LLM error")):
            try:
                agents = create_specialized_agents()
                # If it doesn't crash, that's good
                assert True
            except Exception:
                # Expected in some test environments
                assert True
